--官府任务
--接任务
--【扬州知府通告】近日有恶贼行凶作乱，请各位江湖豪杰速来官府领取铁捕文书，协助将彼等缉拿归案。
add_trigger("guanfu_0", "^[ > ]*【扬州知府通告】近日有恶贼行凶作乱，请各位江湖豪杰速来官府领取铁捕文书，协助将彼等缉拿归案。", function(params)
	var["guanfu_reset"] = nil
end)


add_alias("set_job_gf", function(params)
	close_guanfu()
	var["job_npc_name"] = nil
	var["job_room"] = nil
	var["job_npc_id"] = nil
	var["killer_party"] = nil
	var["gf_target_name"] = "未知npc"
	--	close_huashan_ask()
	--	open_huashan()


	add_alias("kill_job_npc", function(params)
	end)

	var["flag_job"] = "gf"
	set_wield_weapon("gf")
	send("alias bei_skills " .. var["skills_bei2"])

	add_alias("job_escape", function(params) --逃跑
		local killer_name = var["killer_name"] or "none"
		exec("set_danger_list " .. killer_name)
		set_black_list(killer_name)
		var["run"] = get_random_move(var["roomexit"])
		send("look")
		send("set wimpy 100")
		exec("set wimpycmd halt\\@run\\yun qi\\lead @pfm_id\\alias action 战斗失败，立即撤退...")
		exec("halt;get @myweapon;@run;yun qi;lead @pfm_id;alias action 战斗失败，立即撤退...")
	end)

	add_alias("job_ask", function(params) --ask
		function after_gps()
			var["port"] = 121
			function after_goto()
				open_trigger("guanfu_1")
				check_busy(function()
					send("look wanted list")
				end)
			end

			exec("goto @port")
		end

		exec("gps")
	end)
	add_alias("job_fail", function(params)
		var["no_need_heal_qi"] = nil --可以疗伤
		close_guanfu()
		var["gf_target_name"] = "未知npc"
		set_black_list(var["killer_name"])
		--	var["fangqi_job"]="gf"
		exec("drop wenshu;changejob")
	end)

	add_alias("job_win", function(params)
		var["no_need_heal_qi"] = nil --可以疗伤
		close_guanfu()
		var["gf_target_name"] = "未知npc"
		g(149, function()
			b(function()
				if var["job_type"] and var["job_type"] == "缉拿归案" then
					exec("give " .. var["killer_id"] .. " to zhao chengzhi")
				else
					exec("give corpse to zhao chengzhi")
				end
			end)
		end)
	end)

	add_alias("after_faint", function(params) -- 【after_faint】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)

	add_alias("after_faint2", function(params) -- 【after_faint2】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)
	add_alias("npc_win", function(params)
		--close_fight()
		var["job_finish"] = 0
		check_busy2(function()
			exec("yun qi;yun jing;yun jingli;hp;set wimpycmd pfm\\hp;kill @pfm_id;alias action 官府恶贼还在不...")
		end)
	end)
	add_alias("npc_lose", function(params)
		var["job_finish"] = 0
		--	close_fight()--关闭
		check_busy2(function()
			exec("yun qi;yun jing;yun jingli;hp;set wimpycmd pfm\\hp;kill @pfm_id;alias action 官府恶贼还在不...")
		end)
	end)
end)

add_trigger("guanfu_1", "^[ > ]*(?:本府现通缉以下罪犯：|目前治安良好，官府无任何通缉文书贴出。|揭榜请用\\s+jie.*通缉犯.*的格式。)", function(params)
	var["killer_name"] = nil
	var["killer_id"] = nil
	if string.find(params[-1], "本府现通缉以下罪犯") then

		var["guanfu_wanted"] = nil
		var["guanfu_wanted"] = {} --初始化
		add_trigger("guanfu_2", "^\\s+(\\S+)\\((.*)\\)\\s+，经验:\\s+(\\S+)\\s+，", function(params)
			local gf_npc = nil
			gf_npc = {}
			gf_npc["name"] = params[1]
			gf_npc["id"] = string.lower(params[2])
			--		gf_npc["level_name"]=params[3]
			gf_npc["level_name"] = "返璞归真" --新增
			--		gf_npc["level"]=var["user_level"][params[3]]
			gf_npc["level"] = 51 --新增	
			gf_npc["exp"] = tonumber(params[3]) --新增
			--		echo("\n"..C.W..gf_npc["level"])
			var["guanfu_wanted"][params[1]] = gf_npc
		end)
	elseif string.find(params[-1], "目前治安良好，官府无任何通缉文书贴出。") then
		var["guanfu_wanted"] = nil
		--		var["guanfu_wanted"]={} --初始化
		close_trigger("guanfu_1")
		exec("check_gf_job")
	elseif string.find(params[-1], "揭榜请用") then
		close_trigger("guanfu_1")
		del_trigger("guanfu_2")
		echo("\n" .. C.W .. "得到wanted list")
		exec("drop wenshu;check_gf_job")
	end
end)
--var["gf_job_level"]="一代宗师" or "全部"--玩家设定参数
--var["black_list"]={} --黑名单
--var["fear_party"]=""
add_alias("check_gf_job", function(params)
	local guanfu_wanted = var["guanfu_wanted"] or {}
	local gf_job_level = 51 --设置为最高等级，接受所有任务
	--			echo("\n"..C.W..gf_job_level)
	local black_list = var["black_list"] or {}
	if null(guanfu_wanted) then --空的？没任务？
		var["guanfu_reset"] = "wait_refresh"
		var["black_list"] = var["black_list"] or {}
		var["fangqi_job"] = "gf"
		set_black_list(var["killer_name"]) --加入黑名单
		--		var["fangqi_job"]="gf"
		var["gf_target_name"] = "未知npc"
		exec("drop wenshu;changejob")
	else
		--		gf_job_level=var["user_level"][gf_job_level] --转换成数字便于比较
		local gf_new_list = {} --新建一个表
		for k, v in pairs(guanfu_wanted) do
			--		echo("\n"..v.level)
			--echo("\n"..C.W..v.level.." "..v.level_name..":"..gf_job_level)
			if check_black_list(k) then --在blacklist里面
			elseif v.level <= gf_job_level then --比你设定的等级低或相等
				gf_new_list[k] = v
			end
		end
		if null(gf_new_list) then --还是空的。。没找到其他人物吧
			var["guanfu_reset"] = "wait_refresh"
			var["black_list"] = var["black_list"] or {}
			--		var["fangqi_job"]="gf"
			set_black_list(var["killer_name"]) --加入黑名单
			var["fangqi_job"] = "gf"
			var["gf_target_name"] = "未知npc"
			exec("drop wenshu;changejob")
		else
			local get_level, get_name, get_id, get_level_name, get_exp = 51, "", "", "", ""
			local myexp = var["exp"] or 150000                 --新增
			local gf_job_multiple = var["gf_job_multiple"] or 2 --新增
			for k, v in pairs(gf_new_list) do
				if v.level <= get_level and v.exp < myexp * gf_job_multiple then --v.exp<myexp*gf_job_multiple经验倍数			
					get_level = v.level
					get_level_name = v.level_name
					get_name = v.name
					get_id = v.id
					get_exp = v.exp
				end
			end
			var["gf_job_exp"] = get_exp
			if get_name == "" then --还是空的，奇葩
				var["guanfu_reset"] = "wait_refresh"
				var["black_list"] = var["black_list"] or {}
				--	var["fangqi_job"]="gf"
				set_black_list(var["killer_name"]) --加入黑名单
				var["fangqi_job"] = "gf"
				exec("drop wenshu;changejob")
			else
				var["killer_name"] = get_name --好了开始jie 帮吧
				var["gf_target_name"] = get_name or "未知" --目标npc名字已经加入 gf_target_name变量
				var["killer_id"] = get_id
				var["killer_level"] = get_level_name
				echo("\n" .. C.W .. "jie wenshu")
				check_busy(function()
					exec("jie_wenshu") --接文书
				end)
			end
		end
	end
end)

add_alias("jie_wenshu", function(params)
	add_trigger("guanfu_3",
		"你(?:分开行人来到近前，目光淡淡的扫视了几眼告示|推开围观的行人，走到近前，微微睁开眼睛|走上前去看了看，“唰”地一下把通缉|壮了壮胆，费力拔开行人，来到近前深深吸了口气，竭力镇定)",
		function(params)
			del_trigger("guanfu_3")
			del_trigger("guanfu_4")
			del_trigger("guanfu_5")
			exec("look wenshu")
		end)
	add_trigger("guanfu_4",
		"^[ > ]*(?:目前通缉榜上还没出什么官府文书，看来治安还不错|你揭开地上一片瓦片看了看。|此犯的铁捕文书已经全部被揭走了|你想了想，觉得自己的这点功夫，还是算了吧|你揭开地上一块刻.*的方砖看了看)",
		function(params)
			del_trigger("guanfu_3")
			del_trigger("guanfu_4")
			del_trigger("guanfu_5")
			var["fangqi_job"] = "gf"
			exec("drop wenshu;changejob") --失败换任务
		end)
	add_trigger("guanfu_5", "^[ > ]*你现在任务缠身，忙不过来呢", function(params)
		del_trigger("guanfu_3")
		del_trigger("guanfu_4")
		del_trigger("guanfu_5")
		set_dazuo("ask_job_guanfu") --设置打坐
		b(function()
			exec("go_dazuo")  --去打坐
		end)
	end)
	--        此人杀人抢劫后，最后一次出现在佛山小路附近。
	add_trigger("guanfu_6", "^\\s+此人.*后，最后一次出现在(\\S+)附近。", function(params)
		var["job_room"] = params[1]
	end)
	--        此人拦路抢劫后，最后一次出现在沧州东街附近。

	add_trigger("guanfu_7", "^\\s+((?:缉拿归案|就地格杀))的赏金：", function(params)
		del_trigger("guanfu_6")
		del_trigger("guanfu_7")
		var["job_type"] = params[1]
		echo("\n" .. C.W .. var["job_room"] .. " " .. var["job_type"] .. ":" .. var["killer_name"])

		exec("do_gf") --do_gf 启动gf 任务
	end)
	--        缉拿归案的赏金：一锭黄金三十七两白银八十文铜钱。

	check_busy(function()
		exec("jie @killer_id")
	end)
end)

add_alias("do_gf", function(params) --do_gf
	local job_type = var["job_type"] or "就地格杀"
	local job_zone, job_room = break_zone_room(var["job_room"])
	local gf_fangqi_zone = var["gf_fangqi_zone"] or "神龙岛|天山|曼陀罗山庄|燕子坞|姑苏慕容|黑木崖" --默认的是 后面的那个列表
	local gf_fangqi_room = var["gf_fangqi_room"] or "九老洞|后山小院" --默认的是
	if job_type == "缉拿归案" then
		--and string.find("神龙岛|天山|曼陀罗山庄|燕子坞|姑苏慕容|黑木崖",job_zone) then
		var["black_list"] = var["black_list"] or {}
		--	var["fangqi_job"]="gf"
		set_black_list(var["killer_name"]) --加入黑名单
		exec("drop wenshu;changejob")
	else
		var["log_zone"] = job_zone
		var["log_room"] = job_room
		if string.find(gf_fangqi_zone, job_zone) then --如果是放弃搜索区域，那么直接加入黑名单
			var["black_list"] = var["black_list"] or {}
			--		var["fangqi_job"]="gf"		
			set_black_list(var["killer_name"]) --加入黑名单
			var["gf_target_name"] = "未知npc"
			exec("drop wenshu;changejob") --没找到？
		else
			if job_zone == "" and job_room == "" then
				var["black_list"] = var["black_list"] or {}
				--		var["fangqi_job"]="gf"		
				set_black_list(var["killer_name"])                              --加入黑名单
				var["gf_target_name"] = "未知npc"
				exec("drop wenshu;changejob")                                   --没找到？
			else
				local _, room_list, room_list_check = get_room_list(job_zone, job_room, lua_flags) --得到目标房间list
				local _, search_list = get_searches(room_list, 7, lua_flags, 2) --得到范围5的所有房间号,修改为7
				--			table.sort(search_list)
				if null(room_list) then                                         --房间空的？
					var["black_list"] = var["black_list"] or {}
					--		var["fangqi_job"]="gf"
					set_black_list(var["killer_name"]) --加入黑名单
					var["gf_target_name"] = "未知npc"
					exec("drop wenshu;changejob") --没找到？
				else
					local a, b = pathfrom(51, room_list[1], lua_flags), pathfrom(52, room_list[1], lua_flags)
					if a == "" and b == "" then --从房间51 和 52 出发都没路径，没法过去,还是空的...
						var["black_list"] = var["black_list"] or {}
						--		var["fangqi_job"]="gf"
						set_black_list(var["killer_name"]) --加入黑名单
						var["gf_target_name"] = "未知npc"
						exec("drop wenshu;changejob") --没找到？			
					else
						var["job_zone"] = job_zone
						var["job_room"] = job_room
						var["job_range"] = 3
						var["room_list"] = room_list
						var["search_list"] = {}
						var["search_list_2"] = {}
						for k, v in pairs(search_list) do
							var["search_list"][k] = v
							var["search_list_2"][k] = v
						end
						table.sort(var["search_list"])
						table.sort(var["search_list_2"])
						local os_date = os.date("%m/%d %H:%M:%S")

						echo("\n" ..
						C.c ..
						"<Lua>:" ..
						os_date ..
						C.y ..
						"【朝廷公差】:开始寻找最后出现在【" ..
						job_zone .. job_room .. "】的【" .. var["gf_target_name"] .. "】经验【" .. var["gf_job_exp"] .. "】。")
						exec("do_job_gf") --开始做gf 任务 2搜索第二次要搜索两次用do_job_gf
					end
				end
			end
		end
	end
end)                          ------------

function set_black_list(name) --加入到black_list, 共20个，新的插入到第1个删除第20个
	if name then
		local danger = var["black_list"] or {}
		local in_table = 0
		for k, v in pairs(danger) do
			if string.find(name, v) then
				in_table = 1
			end
		end
		if in_table == 0 and name then
			danger[20] = nil
			table.insert(danger, 1, name)
			var["black_list"] = danger
		end
	end
end

function check_black_list(_name) --检查name是不是在black_list 里面
	if _name then
		local black_list = var["black_list"] or {}
		local in_table = false
		for k, v in pairs(black_list) do
			if string.find(v, _name) then
				in_table = true
			end
		end
		return in_table
	else
		return false
	end
end

add_alias("do_job_gf", function(params)
	local search_list = var["search_list"] or {}
	if null(search_list) then
		exec("job_fail") --任务失败
	else
		open_guanfu()
		add_trigger("guanfu_8", "^\\s+(\\S+)(?:叛徒|弃徒)\\s+" .. var["killer_name"], function(params)
			var["killer_party"] = params[1]
			add_trigger("guanfu_9", "^□(\\S+)\\(", function(params)
				del_trigger("guanfu_9")
				var["killer_skill"] = params[1]
			end)
		end)
		add_trigger("guanfu_9", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" .. var["killer_name"] .. "神志迷糊，脚下一个不稳，倒在地上昏了过去。",
			function(params)
				unset_timer("alarm1")
				close_trigger("guanfu_18")

				if var["job_type"] and var["job_type"] == "缉拿归案" and 1 == 0 then
					exec("set wimpycmd halt")
					fight_end()
					do_log("gf_win")
					bb(function()
						exec("get gold from @killer_id;get silver from @killer_id;get @killer_id;job_win")
					end)
				else
					exec("halt;kill " .. var["killer_id"])
				end
			end)
		add_trigger("guanfu_10", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" .. var["killer_name"] .. "「啪」的一声倒在地上，挣扎着抽动了几下就死了。",
			function(params)
				unset_timer("alarm1")
				close_trigger("guanfu_18")
				open_trigger("guanfu_23")
				add_alias("job_win", function(params)
					g(149, function()
						b(function()
							exec("give corpse to zhao chengzhi")
						end)
					end)
				end)
				fight_end()
				do_log("gf_win")
				bb(function()
					exes("get gold from corpse;get silver from corpse;get corpse;alias action JOB FINISH and GET CORPSE",
						5)
				end)
			end)
		add_trigger("guanfu_11", "^[ > ]*你.*道：承让.*！$", function(params)
			exec("npc_lose")
		end)
		add_trigger("guanfu_12", "^[ > ]*你.*说道：.*(?:佩服|果然高明|在下输了)！$", function(params)
			exec("npc_win")
		end)
		add_trigger("guanfu_13", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" ..
		var["killer_name"] .. ".*说道：.*(?:佩服|果然高明|在下输了)！$", function(params)
			exec("npc_lose")
		end)
		add_trigger("guanfu_14", "^[ > ]*" .. var["killer_name"] .. ".*道：承让.*！$", function(params)
			exec("npc_win")
		end)

		function after_gps()
			add_alias("cross_for_job", function(params) -- cross_for_job 走路中停顿
				check_busy(function()
					exes("jubu @killer_id;follow @killer_id;look @killer_id;alias action 奉ET大人命寻找贼寇...", 5)
				end)
			end)
			local search_list = var["search_list"]
			local do_job = ""
			------设置快速扫街job var["quick_job"] 参数
			local quick_job = 0

			if quick_job == 0 then
				add_alias("kill_job_npc", function(params)
					if var["job_type"] and var["job_type"] == "缉拿归案" then
						exec("hit " .. var["killer_id"])
					else
						--	exec("hit "..var["killer_id"])
						exec("kill " .. var["killer_id"])
					end
				end)
				var["do_job"] = ""
				add_alias("do_job", function(params) -- do_job
					--			send("ask "..var["killer_id"].." about bamao")
				end)
				do_job = "cross_for_job"
			else
				--[[
				var["pfm_id"]=get_id(var["job_npc_name"])
				var["do_job"]="ask "..var["pfm_id"].." about ET外星人"
				add_alias("do_myjob",function(params) -- do_myjob和do_job区分一下，这个alias 将删除走过的房间
					table.remove(var["search_list"],1)
					send("ask "..var["pfm_id"].." about ET外星人")
				end)
				add_alias("do_job",function(params) -- do_job
					send("ask "..var["pfm_id"].." about ET外星人")
				end)
				do_job="do_myjob"
				]]
			end
			-----

			if null(search_list) == true then
				exec("job_fail")
			else
				local search_path, port = get_searches_path(search_list, do_job, lua_flags)

				var["port"] = port
				var["search_path"] = search_path

				function after_goto()
					function after_goto()
						exec("do_job_gf_2")
					end

					var["path_after"] = var["search_path"]
					--		echo("\n"..C.W..var["path_after"])
					exec("yun_powerup keepwalk")
				end

				--		echo("port"..var["port"])
				exec("goto @port")
			end
		end

		exec("gps")
	end
end)

add_alias("do_job_gf_2", function(params)
	var["search_list"] = nil
	var["search_list"] = {}
	for k, v in pairs(var["search_list_2"]) do
		var["search_list"][k] = v
	end
	local search_list = nil
	search_list = var["search_list"] or {}
	table.sort(search_list)
	if null(search_list) then
		exec("job_fail") --任务失败
	else
		function after_gps()
			add_alias("cross_for_job", function(params) -- cross_for_job 走路中停顿
				check_busy(function()
					exes("follow @killer_id;jubu @killer_id;look @killer_id;alias action 奉ET大人命寻找贼寇...", 5)
				end)
			end)
			local search_list = var["search_list"]
			local do_job = ""
			------设置快速扫街job var["quick_job"] 参数
			local quick_job = 0

			if quick_job == 0 then
				add_alias("kill_job_npc", function(params)
					if var["job_type"] and var["job_type"] == "缉拿归案" then
						exec("hit " .. var["killer_id"])
					else
						exec("hit " .. var["killer_id"])
					end
				end)
				var["do_job"] = ""
				add_alias("do_job", function(params) -- do_job
					--			send("ask "..var["killer_id"].." about bamao")
				end)
				do_job = "cross_for_job"			
			end
			-----

			if null(search_list) == true then
				exec("job_fail")
			else
				local search_path, port = get_searches_path(search_list, do_job, lua_flags)

				var["port"] = port
				var["search_path"] = search_path

				function after_goto()
					function after_goto()
						exec("alias action 八婺天下说累死人不偿命，收工...")
					end

					var["path_after"] = var["search_path"]
					--		echo("\n"..C.W..var["path_after"])
					exec("yun_powerup keepwalk")
				end

				--		echo("port"..var["port"])
				exec("goto @port")
			end
		end

		exec("gps")
	end
end)
--触发
--灵鹫宫叛徒 乐勇森(Yue yongsen)
--他看起来约三十多岁。
--他的武艺看上去无与伦比，出手似乎极重。
--他看起来气血充盈，并没有受伤。
--他穿戴着：
--  □白边黄色长袍(Chang pao)
-->


add_trigger("guanfu_15", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"奉ET大人命寻找贼寇", function(params)
	del_timer("input")
	table.remove(var["search_list"], 1)
	local do_stop = var["do_stop"] or 0
	if do_stop == 0 then
		local get_npc = check_room_obj(var["killer_name"])
		if get_npc == false then
			keepwalk()
		else
			var["do_stop"] = 1
			send("look " .. var["killer_id"])
			send("alias action 八婺天下说看看情况再动手...")
		end
	end
end)
add_trigger("guanfu_16", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"八婺天下说看看情况再动手", function(params)
	del_timer("input")
	local fear_party = var["fear_party"] or "none"
	local killer_party = var["killer_party"] or "none"
	local killer_skill = var["killer_skill"] or "none"
	--fear_party="武当派|星宿派|古墓派"--预设几个看看？
	echo("\n" .. C.W .. killer_party .. " " .. killer_skill)
	if string.find(fear_party, killer_party) then
		var["black_list"] = var["black_list"] or {}
		var["fangqi_job"] = "gf"
		set_black_list(var["killer_name"]) --加入黑名单
		exec("drop wenshu;changejob") --没找到？
	else
		var["job_finish"] = 0
		var["pfm_id"] = var["killer_id"]
		set_fight("gf")
		exec("kill_job_npc")
		alarm("input", 20, function()
			exec("look;alias action JOB NPC ESCAPED OR NOT")
		end)
	end
end)
--你把 "action" 设定为 "八婺天下说累死人不偿命，收工..." 成功完成。

add_trigger("guanfu_17", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"八婺天下说累死人不偿命，收工", function(params)
	del_timer("input")
	var["black_list"] = var["black_list"] or {}
	var["fangqi_job"] = "gf"
	set_black_list(var["killer_name"]) --加入黑名单
	exec("drop wenshu;changejob")     --没找到？
	local os_date = os.date("%m/%d %H:%M:%S")
	close_guanfu()
	echo("\n" .. C.c .. "<Lua>:" ..
	os_date .. C.y .. "【朝廷公差】:揭榜之后没找到武功【" .. var["killer_level"] .. "】的【" .. var["killer_name"] .. "】。")
end)
--你把 "action" 设定为 "JOB FINISH and GET CORPSE" 成功完成。
add_trigger("guanfu_18", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"JOB NPC ESCAPED OR NOT", function(params)
	local get_npc = check_room_obj(var["killer_name"])
	if get_npc == false then
		unset_timer("timer")
		var["do_stop"] = 0
		exec("do_job_gf_2")
	else
		alarm("input", 10, function()
			exec("look;alias action JOB NPC ESCAPED OR NOT...")
		end)
	end
end)
add_trigger("guanfu_19", "^[ > ]*(\\S+)将(\\S+)的尸体扶了起来背在背上。", function(params)
	var["idle"] = 0
	local killer_name = var["gf_target_name"] or "未知npc"
	if trim(params[1]) == "你" then --本人杀掉的，并且由本人get corpse
		if trim(params[2]) == killer_name then
			unset_timer("alarm1")
			close_trigger("guanfu_18")
			unset_timer("wait")
			exec("job_win")
			echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.g .. "【朝廷公差】:搞定【" .. killer_name ..
			"】可以回去交差了！")
		else
			wait(20, function()
				exec("job_win")
			end)
			exec("drop corpse;get corpse 2")
		end
	else
		if trim(params[2]) == killer_name then --尸体被别人get ，那么直接任务失败
			close_guanfu()
			close_trigger("guanfu_19")
			set_black_list(trim(params[2])) --被抢走的尸体名字加入黑名单
			local os_date = os.date("%m/%d %H:%M:%S")
			echo("\n" ..
			C.c ..
			"<Lua>:" .. os_date ..
			C.r .. "【朝廷公差】:已经杀死【" .. killer_name .. "】，但是" .. killer_name .. "的尸体被【" .. trim(params[1]) .. "】抢走了！")

			b(function()
				exec("drop wenshu;changejob")
			end)
		end
	end
end)


--> 你给赵城之一具郑雄的尸体。
--赵城之说道：「哦，郑雄的尸体！多谢这位婆婆了，你下去吧。」
--赵城之说道：「下一个！」

--> 你给赵城之一具澹台部的尸体。
add_trigger("guanfu_20", "^[ > ]*你给赵城之一具(.*)的(尸体|男尸|女尸)", function(params) -- 你给赵城之一具(\\S+)的尸体
	var["idle"] = 0
	var["gf_corpse_name"] = params[1] or ""
	close_guanfu()
	b(function()
		exec("drop wenshu;changejob")
	end)
end)
--你得到了赏金一锭黄金三十六两白银二十文铜钱，四百五十四点潜能和一千三百六十二点经验！
add_trigger("guanfu_reward_1", "^[ > ]*你得到了赏金.*，(\\S+)点潜能和(\\S+)点经验！", function(params)
	var["log_pot"] = params[1]
	var["log_exp"] = params[2]

	do_log("job_reward")
end)
--赵城之说道：「哦，焦鼎的尸体！多谢这位婆婆了，你下去吧。」
--尸体不是我自己杀的，可能抢了别人的，用这句触发进行下一句话

--赵城之说道：「没看见本官正忙着吗？一边等着！」
add_trigger("guanfu_23", "^[ > ]*赵城之说道：「没看见本官正忙着吗？一边等着！」", function(params)
	local os_date = os.date("%m/%d %H:%M:%S")
	echo("\n" .. C.c .. "<Lua>:" .. os_date .. C.r .. "【朝廷公差】:尸体没送出去，赵城之忙呢！！")
	wait(2, function()
		exes("give corpse to zhao chengzhi", 2)
		echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.r .. "【朝廷公差】:尸体再送一遍！")
	end)
end)
add_trigger("guanfu_24", "^[ > ]*这个尸体不属于你", function(params)
	exec("drop corpse;get corpse 2")
end)
--alias action 官府恶贼还在不...
function open_guanfu()
	open_trigger("guanfu_1")
	open_trigger("guanfu_15")
	open_trigger("guanfu_16")
	open_trigger("guanfu_17")
	open_trigger("guanfu_18")
	open_trigger("guanfu_19")
	open_trigger("guanfu_20")
	open_trigger("guanfu_21")
	open_trigger("guanfu_22")
	open_trigger("guanfu_23")
	open_trigger("guanfu_24")
end

function close_guanfu()
	close_trigger("guanfu_1")
	del_trigger("guanfu_2")
	del_trigger("guanfu_3")
	del_trigger("guanfu_4")
	del_trigger("guanfu_5")
	del_trigger("guanfu_6")
	del_trigger("guanfu_7")
	del_trigger("guanfu_8")
	del_trigger("guanfu_9")
	del_trigger("guanfu_10")
	del_trigger("guanfu_11")
	del_trigger("guanfu_12")
	del_trigger("guanfu_13")
	del_trigger("guanfu_14")
	close_trigger("guanfu_15")
	close_trigger("guanfu_16")
	close_trigger("guanfu_17")
	close_trigger("guanfu_18")
	close_trigger("guanfu_19")
	close_trigger("guanfu_20")
	close_trigger("guanfu_21")
	close_trigger("guanfu_22")
	close_trigger("guanfu_23")
	close_trigger("guanfu_24")
end
